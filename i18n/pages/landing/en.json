{"template": "shipany-template-one", "theme": "light", "header": {"brand": {"title": "YAY! COLORING PAGES", "logo": {"src": "/logo.png", "alt": "YAY! COLORING PAGES"}, "url": "/"}, "nav": {"items": [{"title": "COLORING PAGES", "url": "/#feature", "icon": "HiOutlineSparkles"}, {"title": "TOOLS & TRICKS", "url": "/#usage", "icon": "MdPayment"}, {"title": "ABOUT", "url": "/#introduce", "icon": "BiCube"}, {"title": "CONTACT", "url": "/#cta", "icon": "MdContactMail"}]}, "buttons": [{"title": "Free Download", "url": "/#feature", "target": "_self", "variant": "link", "icon": "RiDownloadLine"}], "show_sign": true, "show_theme": true, "show_locale": true}, "hero": {"title": "Free Printable Coloring Pages & Birthday Cards", "highlight_text": "YAY!", "description": "Explore a variety of free, printable coloring pages for all ages.<br/>Unleash your creativity with endless coloring fun!", "announcement": {"label": "New", "title": "✨ New Collection Available", "url": "/#feature"}, "tip": "🎨 Completely Free, Download Now", "buttons": [{"title": "Browse Coloring Pages", "icon": "RiPaletteFill", "url": "/#feature", "target": "_self", "variant": "default"}, {"title": "Get Free Coloring Book", "icon": "RiDownloadFill", "url": "/#feature", "target": "_self", "variant": "outline"}], "show_happy_users": false, "show_badge": false}, "branding": {"title": "ShipAny is built on the shoulders of giants", "items": [{"title": "Next.js", "image": {"src": "/imgs/logos/nextjs.svg", "alt": "Next.js"}}, {"title": "React", "image": {"src": "/imgs/logos/react.svg", "alt": "React"}}, {"title": "TailwindCSS", "image": {"src": "/imgs/logos/tailwindcss.svg", "alt": "TailwindCSS"}}, {"title": "Shadcn/UI", "image": {"src": "/imgs/logos/shadcn.svg", "alt": "Shadcn/UI"}}, {"title": "Vercel", "image": {"src": "/imgs/logos/vercel.svg", "alt": "Vercel"}}]}, "introduce": {"name": "introduce", "title": "About YAY! Coloring Pages", "label": "About Us", "description": "YAY! Coloring Pages offers free printable coloring pages and tutorials for endless creative fun for all ages.", "image": {"src": "/imgs/features/1.png"}, "items": [{"title": "Completely Free", "description": "All coloring pages are completely free to download and print anytime.", "icon": "RiGiftFill"}, {"title": "High-Quality Designs", "description": "Carefully crafted line art perfect for printing and digital coloring.", "icon": "RiPaletteFill"}, {"title": "Various Categories", "description": "Animals, holidays, architecture, fantasy themes and more. New content added regularly.", "icon": "RiGridFill"}]}, "benefit": {"name": "benefit", "title": "Why Choose ShipAny", "label": "Benefits", "description": "Get everything you need to launch your AI startup - from ready-to-use templates to technical support.", "items": [{"title": "Complete Framework", "description": "Built on Next.js with authentication, payments, and AI integration - everything works out of the box.", "icon": "RiNextjsFill", "image": {"src": "/imgs/features/2.png"}}, {"title": "Rich Templates Library", "description": "Choose from various AI SaaS templates to kickstart your project - chatbots, image generation, and more.", "icon": "RiClapperboardAiLine", "image": {"src": "/imgs/features/3.png"}}, {"title": "Technical Guidance", "description": "Get dedicated support and join our developer community to ensure your successful launch.", "icon": "RiCodeFill", "image": {"src": "/imgs/features/4.png"}}]}, "usage": {"name": "usage", "title": "How to Launch with ShipAny", "description": "Get your AI SaaS startup running in three simple steps:", "image": {"src": "/imgs/features/1.png"}, "image_position": "left", "text_align": "center", "items": [{"title": "Get ShipAny", "description": "Buy ShipAny with a one-time payment. Check your email for the code and documentation.", "image": {"src": "/imgs/features/5.png"}}, {"title": "Start Your Project", "description": "Read the documentation and clone the code of ShipAny. Start building your AI SaaS startup.", "image": {"src": "/imgs/features/6.png"}}, {"title": "Customize Your Project", "description": "Modify the template with your data and contents. Specific AI functionality needs.", "image": {"src": "/imgs/features/7.png"}}, {"title": "Deploy to Production", "description": "Deploy your project to production with a few steps and start serving customers immediately.", "image": {"src": "/imgs/features/8.png"}}]}, "feature": {"name": "feature", "title": "Unleash Your Creativity", "label": "Features", "description": "YAY! Coloring Pages offers free printable coloring pages and tutorials for endless creative fun.", "items": [{"title": "Free Printable PDFs", "description": "Download high-quality coloring pages in PDF format. Perfect for printing at home or coloring digitally on your device.", "icon": "RiDownloadFill"}, {"title": "Digital Coloring", "description": "Color on your computer or tablet using apps like Procreate. We provide tutorials for digital coloring techniques.", "icon": "RiPaletteFill"}, {"title": "For All Ages", "description": "From simple designs for kids to intricate patterns for adults. Everyone can find something they love to color.", "icon": "RiGroupFill"}, {"title": "Various Categories", "description": "Explore animals, holidays, architecture, fantasy themes and more. New collections added regularly.", "icon": "RiGridFill"}, {"title": "Tips & Tutorials", "description": "Learn coloring techniques, color theory, and digital art tips to enhance your creative journey.", "icon": "RiBookOpenFill"}, {"title": "Stress Relief", "description": "Coloring is a great way to relax, reduce stress, and express your creativity in a mindful way.", "icon": "RiHeartFill"}]}, "showcase": {"name": "showcase", "title": "AI SaaS Startups built with ShipAny", "description": "Easy to use and fast to ship.", "items": [{"title": "ThinkAny", "description": "AI Search Engine", "url": "https://thinkany.ai", "target": "_blank", "image": {"src": "/imgs/showcases/7.png"}}, {"title": "HeyBeauty", "description": "AI Virtual Try On", "url": "https://heybeauty.ai", "target": "_blank", "image": {"src": "/imgs/showcases/5.png"}}, {"title": "AI Wallpaper", "description": "AI Wallpaper Generator", "url": "https://aiwallpaper.shop", "target": "_blank", "image": {"src": "/imgs/showcases/1.png"}}, {"title": "AI Cover", "description": "AI Cover Generator", "url": "https://aicover.design", "target": "_blank", "image": {"src": "/imgs/showcases/2.png"}}, {"title": "GPTs Works", "description": "GPTs Directory", "url": "https://gpts.works", "target": "_blank", "image": {"src": "/imgs/showcases/3.png"}}, {"title": "Melod<PERSON>", "description": "AI Music Player", "url": "https://melodis.co", "target": "_blank", "image": {"src": "/imgs/showcases/4.png"}}, {"title": "<PERSON><PERSON>", "description": "AI Landing Page Generator", "url": "https://pagen.so", "target": "_blank", "image": {"src": "/imgs/showcases/6.png"}}, {"title": "SoraFM", "description": "AI Video Generator", "url": "https://sorafm.trys.ai", "target": "_blank", "image": {"src": "/imgs/showcases/8.png"}}, {"title": "PodLM", "description": "AI Podcast Generator", "url": "https://podlm.ai", "target": "_blank", "image": {"src": "/imgs/showcases/9.png"}}]}, "stats": {"name": "stats", "label": "Stats", "title": "People Love ShipAny", "description": "for it's easy to use and fast to ship.", "icon": "FaRegHeart", "items": [{"title": "Trusted by", "label": "99+", "description": "Customers"}, {"title": "Built in", "label": "20+", "description": "Components"}, {"title": "Ship Fast in", "label": "5", "description": "Minutes"}]}, "pricing": {"name": "pricing", "label": "Pricing", "title": "Pricing", "description": "Get all features of ShipAny, Ship your AI SaaS startups fast.", "groups": [], "items": [{"title": "Starter", "description": "Get started with your first SaaS startup.", "features_title": "Includes", "features": ["100 credits, valid for 1 month", "NextJS boilerplate", "SEO-friendly structure", "Payment with Stripe", "Data storage with Supabase", "Google Oauth & One-Tap Login", "i18n support"], "interval": "one-time", "amount": 9900, "cn_amount": 69900, "currency": "USD", "price": "$99", "original_price": "$199", "unit": "USD", "is_featured": false, "tip": "Pay once. Build unlimited projects!", "button": {"title": "Get ShipAny", "url": "/#pricing", "icon": "RiFlashlightFill"}, "product_id": "starter", "product_name": "ShipAny <PERSON>ilerplate Starter", "credits": 100, "valid_months": 1}, {"title": "Standard", "description": "Ship Fast with your SaaS Startups.", "label": "Popular", "features_title": "Everything in Starter, plus", "features": ["200 credits, valid for 3 month", "Deploy with Vercel or Cloudflare", "Generation of Privacy & Terms", "Google Analytics Integration", "Google Search Console Integration", "Discord community", "Technical support for your first ship", "Lifetime updates"], "interval": "one-time", "amount": 19900, "cn_amount": 139900, "currency": "USD", "price": "$199", "original_price": "$299", "unit": "USD", "is_featured": true, "tip": "Pay once. Build unlimited projects!", "button": {"title": "Get ShipAny", "url": "/#pricing", "icon": "RiFlashlightFill"}, "product_id": "standard", "product_name": "ShipAny Boilerplate Standard", "credits": 200, "valid_months": 3}, {"title": "Premium", "description": "Ship Any AI SaaS Startups.", "features_title": "Everything in Standard, plus", "features": ["300 credits, valid for 1 year", "Business Functions with AI", "User Center", "Credits System", "API Sales for your SaaS", "Admin System", "Priority Technical Support"], "interval": "one-time", "amount": 29900, "cn_amount": 199900, "currency": "USD", "price": "$299", "original_price": "$399", "unit": "USD", "is_featured": false, "tip": "Pay once. Build unlimited projects!", "button": {"title": "Get ShipAny", "url": "/#pricing", "icon": "RiFlashlightFill"}, "product_id": "premium", "product_name": "ShipAny Boilerplate Premium", "credits": 300, "valid_months": 12}]}, "testimonial": {"name": "testimonial", "label": "Testimonial", "title": "What Users Say About ShipAny", "description": "Hear from developers and founders who launched their AI startups with ShipAny.", "icon": "GoThumbsup", "items": [{"title": "<PERSON>", "label": "Founder of AIWallpaper.shop", "description": "ShipAny saved us months of development time. We launched our AI wallpaper business in just 2 days and got our first paying customer within a week!", "image": {"src": "/imgs/users/1.png"}}, {"title": "<PERSON>", "label": "CTO at HeyBeauty.ai", "description": "The pre-built AI infrastructure is a game-changer. We didn't have to worry about architecture - just focused on our AI beauty tech and went live fast.", "image": {"src": "/imgs/users/2.png"}}, {"title": "<PERSON>", "label": "Solo Developer", "description": "As a solo developer, <PERSON><PERSON><PERSON> gave me everything I needed - auth, payments, AI integration, and beautiful UI. Launched my SaaS in a weekend!", "image": {"src": "/imgs/users/3.png"}}, {"title": "<PERSON> Garcia", "label": "CEO of Melodisco", "description": "The templates are production-ready and highly customizable. We built our AI music platform in hours instead of months. Incredible time-to-market!", "image": {"src": "/imgs/users/4.png"}}, {"title": "<PERSON>", "label": "Tech Lead at GPTs.works", "description": "ShipAny's infrastructure is rock-solid. We scaled from 0 to 10k users without touching the backend. Best investment for our AI startup.", "image": {"src": "/imgs/users/5.png"}}, {"title": "<PERSON>", "label": "Startup Founder", "description": "From idea to launch in 3 days! ShipAny's templates and deployment tools made it possible to test our AI business concept incredibly fast.", "image": {"src": "/imgs/users/6.png"}}]}, "faq": {"name": "faq", "label": "FAQ", "title": "Frequently Asked Questions About ShipAny", "description": "Have another question? Contact us on Discord or by email.", "items": [{"title": "What exactly is <PERSON><PERSON><PERSON> and how does it work?", "description": "ShipAny is a comprehensive NextJS boilerplate designed specifically for building AI SaaS startups. It provides ready-to-use templates, infrastructure setup, and deployment tools that help you launch your AI business in hours instead of days."}, {"title": "Do I need advanced technical skills to use ShipAny?", "description": "While basic programming knowledge is helpful, ShipAny is designed to be developer-friendly. Our templates and documentation make it easy to get started, even if you're not an expert in AI or cloud infrastructure."}, {"title": "What types of AI SaaS can I build with ShipAny?", "description": "ShipAny supports a wide range of AI applications, from content generation to data analysis tools. Our templates cover popular use cases like AI chatbots, content generators, image processing apps, and more."}, {"title": "How long does it typically take to launch with ShipAny?", "description": "With ShipAny, you can have a working prototype in hours and a production-ready application in hours. Our one-click deployment and pre-configured infrastructure significantly reduce the traditional months-long development cycle."}, {"title": "What's included in the ShipAny infrastructure?", "description": "ShipAny provides a complete infrastructure stack including authentication, database setup, API integration, payment processing, and scalable cloud deployment. Everything is pre-configured following industry best practices."}, {"title": "Can I customize the templates to match my brand?", "description": "Absolutely! All ShipAny templates are fully customizable. You can modify the design, features, and functionality to match your brand identity and specific business requirements while maintaining the robust underlying infrastructure."}]}, "cta": {"name": "cta", "title": "Ship your first AI SaaS Startup", "description": "Start from here, ship with ShipAny.", "buttons": [{"title": "Get ShipAny", "url": "https://shipany.ai", "target": "_blank", "icon": "GoArrowUpRight"}, {"title": "Read Document", "url": "https://docs.shipany.ai", "target": "_blank", "variant": "outline"}]}, "footer": {"name": "footer", "brand": {"title": "ShipAny", "description": "ShipAny is a NextJS boilerplate for building AI SaaS startups. Ship Fast with a variety of templates and components.", "logo": {"src": "/logo.png", "alt": "ShipAny"}, "url": "/"}, "copyright": "© 2025 • ShipAny All rights reserved.", "nav": {"items": [{"title": "About", "children": [{"title": "Features", "url": "/#feature", "target": "_self"}, {"title": "Showcases", "url": "/#showcase", "target": "_self"}, {"title": "Pricing", "url": "/#pricing", "target": "_self"}]}, {"title": "Resources", "children": [{"title": "Documents", "url": "https://docs.shipany.ai", "target": "_blank"}, {"title": "Components", "url": "https://shipany.ai/components", "target": "_blank"}, {"title": "Templates", "url": "https://shipany.ai/templates", "target": "_blank"}]}, {"title": "Friends", "children": [{"title": "ThinkAny", "url": "https://thinkany.ai", "target": "_blank"}, {"title": "HeyBeauty", "url": "https://heybeauty.ai", "target": "_blank"}, {"title": "<PERSON><PERSON>", "url": "https://pagen.so", "target": "_blank"}]}]}, "social": {"items": [{"title": "X", "icon": "RiTwitterXFill", "url": "https://x.com/shipanyai", "target": "_blank"}, {"title": "<PERSON><PERSON><PERSON>", "icon": "RiGithubFill", "url": "https://github.com/shipanyai", "target": "_blank"}, {"title": "Discord", "icon": "RiDiscordFill", "url": "https://discord.gg/HQNnrzjZQS", "target": "_blank"}, {"title": "Email", "icon": "RiMailLine", "url": "mailto:<EMAIL>", "target": "_self"}]}, "agreement": {"items": [{"title": "Privacy Policy", "url": "/privacy-policy"}, {"title": "Terms of Service", "url": "/terms-of-service"}]}}}