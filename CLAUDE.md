# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
- `pnpm dev` - Start development server (uses NODE_NO_WARNINGS=1)
- `pnpm build` - Build for production
- `pnpm start` - Start production server
- `pnpm lint` - Run ESLint

### Analysis & Deployment
- `pnpm analyze` - Build with bundle analyzer (ANALYZE=true pnpm build)
- `pnpm cf:build` - Build for Cloudflare Pages
- `pnpm cf:preview` - Preview Cloudflare deployment locally
- `pnpm cf:deploy` - Deploy to Cloudflare Pages

## Architecture

This is a Next.js 14 TypeScript application using App Router with internationalization and AI capabilities.

### Core Stack
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + Shadcn UI components
- **State**: React Context
- **Auth**: NextAuth.js 5.0 beta
- **i18n**: next-intl with locale routing
- **Payment**: Stripe integration
- **Database**: Supabase
- **AI**: Multiple AI providers (OpenAI, Replicate, OpenRouter) via AI SDK

### Key Directory Structure
- `app/[locale]/` - Locale-specific pages and layouts
- `app/api/` - API routes (checkout, auth, demo endpoints)
- `components/blocks/` - Landing page layout blocks (hero, pricing, etc.)
- `components/ui/` - Reusable Shadcn UI components
- `i18n/pages/landing/` - Page-specific translations
- `i18n/messages/` - Global translation messages
- `models/` - Data models and database operations
- `services/` - Business logic layer
- `types/blocks/` - TypeScript definitions for layout blocks
- `aisdk/` - Custom AI SDK with video generation capabilities

### Authentication & Routing
- Uses NextAuth.js with multiple providers
- Locale-based routing with middleware
- Protected routes for admin and console areas
- Session management with React Context

### AI Integration
- Custom AI SDK in `aisdk/` directory
- Video generation capabilities (Kling provider)
- Image generation support
- Multiple AI model providers integrated

### Internationalization
- Built with next-intl
- Supports English (en) and Chinese (zh)
- Locale-specific routing: `/[locale]/path`
- Landing page content configurable per locale

### Environment Setup
- Copy `.env.example` to `.env.local` for development
- For Cloudflare: use `.env.production` and configure `wrangler.toml`
- Theme customization in `app/theme.css`

### Component Conventions
- Use functional components with TypeScript
- CamelCase component names
- Modular and reusable design
- Proper type definitions in `types/` directory