import Icon from "@/components/icon";
import { Section as SectionType } from "@/types/blocks/section";

export default function Feature({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-20 bg-white">
      <div className="container">
        <div className="mx-auto flex max-w-screen-md flex-col items-center gap-6 text-center mb-16">
          {section.label && (
            <div className="inline-block px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm font-medium uppercase tracking-wide">
              {section.label}
            </div>
          )}
          {section.title && (
            <h2 className="mb-4 text-pretty text-4xl font-bold lg:text-5xl text-foreground">
              {section.title}
            </h2>
          )}
          {section.description && (
            <p className="mb-8 max-w-3xl text-muted-foreground lg:text-xl leading-relaxed">
              {section.description}
            </p>
          )}
        </div>

        {section.items && section.items.length > 0 && (
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 mb-16">
            {section.items.map((item, index) => (
              <div key={index} className="group flex flex-col p-8 rounded-lg bg-card border border-border hover:border-primary/20 transition-all duration-300 hover:shadow-lg">
                {item.icon && (
                  <div className="mb-6 flex size-16 items-center justify-center rounded-lg bg-primary text-primary-foreground group-hover:scale-110 transition-transform duration-300">
                    <Icon name={item.icon} className="size-8" />
                  </div>
                )}
                {item.title && (
                  <h3 className="mb-3 text-xl font-semibold text-foreground">
                    {item.title}
                  </h3>
                )}
                {item.description && (
                  <p className="text-muted-foreground leading-relaxed">
                    {item.description}
                  </p>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
}
