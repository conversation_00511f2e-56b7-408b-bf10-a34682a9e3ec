import Icon from "@/components/icon";
import { Section as SectionType } from "@/types/blocks/section";

export default function Feature({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-20 bg-white">
      <div className="container">
        <div className="mx-auto flex max-w-screen-md flex-col items-center gap-6 text-center mb-16">
          <h2 className="mb-4 text-pretty text-4xl font-bold lg:text-5xl text-gray-800">
            Unleash Your Creativity
          </h2>
          <p className="mb-8 max-w-3xl text-gray-600 lg:text-xl leading-relaxed">
            Yay! Coloring Pages offers free printable coloring pages and tutorials for endless creative fun.
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 mb-16">
          <div className="group flex flex-col p-8 rounded-3xl bg-gradient-to-br from-pink-50 to-pink-100 border border-pink-200 hover:border-pink-300 transition-all duration-300 hover:shadow-lg hover:shadow-pink-100">
            <div className="mb-6 flex size-20 items-center justify-center rounded-2xl bg-gradient-to-br from-pink-400 to-pink-500 border border-pink-300 group-hover:scale-110 transition-transform duration-300">
              <Icon name="Download" className="size-10 text-white" />
            </div>
            <h3 className="mb-3 text-xl font-semibold text-gray-800">Free Printable PDFs</h3>
            <p className="text-gray-600 leading-relaxed">Download high-quality coloring pages in PDF format. Perfect for printing at home or coloring digitally on your device.</p>
          </div>

          <div className="group flex flex-col p-8 rounded-3xl bg-gradient-to-br from-cyan-50 to-cyan-100 border border-cyan-200 hover:border-cyan-300 transition-all duration-300 hover:shadow-lg hover:shadow-cyan-100">
            <div className="mb-6 flex size-20 items-center justify-center rounded-2xl bg-gradient-to-br from-cyan-400 to-cyan-500 border border-cyan-300 group-hover:scale-110 transition-transform duration-300">
              <Icon name="Palette" className="size-10 text-white" />
            </div>
            <h3 className="mb-3 text-xl font-semibold text-gray-800">Digital Coloring</h3>
            <p className="text-gray-600 leading-relaxed">Color on your computer or tablet using apps like Procreate. We provide tutorials for digital coloring techniques.</p>
          </div>

          <div className="group flex flex-col p-8 rounded-3xl bg-gradient-to-br from-yellow-50 to-yellow-100 border border-yellow-200 hover:border-yellow-300 transition-all duration-300 hover:shadow-lg hover:shadow-yellow-100">
            <div className="mb-6 flex size-20 items-center justify-center rounded-2xl bg-gradient-to-br from-yellow-400 to-yellow-500 border border-yellow-300 group-hover:scale-110 transition-transform duration-300">
              <Icon name="Users" className="size-10 text-white" />
            </div>
            <h3 className="mb-3 text-xl font-semibold text-gray-800">For All Ages</h3>
            <p className="text-gray-600 leading-relaxed">From simple designs for kids to intricate patterns for adults. Everyone can find something they love to color.</p>
          </div>

          <div className="group flex flex-col p-8 rounded-3xl bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 hover:border-purple-300 transition-all duration-300 hover:shadow-lg hover:shadow-purple-100">
            <div className="mb-6 flex size-20 items-center justify-center rounded-2xl bg-gradient-to-br from-purple-400 to-purple-500 border border-purple-300 group-hover:scale-110 transition-transform duration-300">
              <Icon name="Grid" className="size-10 text-white" />
            </div>
            <h3 className="mb-3 text-xl font-semibold text-gray-800">Various Categories</h3>
            <p className="text-gray-600 leading-relaxed">Explore animals, holidays, architecture, fantasy themes and more. New collections added regularly.</p>
          </div>

          <div className="group flex flex-col p-8 rounded-3xl bg-gradient-to-br from-green-50 to-green-100 border border-green-200 hover:border-green-300 transition-all duration-300 hover:shadow-lg hover:shadow-green-100">
            <div className="mb-6 flex size-20 items-center justify-center rounded-2xl bg-gradient-to-br from-green-400 to-green-500 border border-green-300 group-hover:scale-110 transition-transform duration-300">
              <Icon name="BookOpen" className="size-10 text-white" />
            </div>
            <h3 className="mb-3 text-xl font-semibold text-gray-800">Tips & Tutorials</h3>
            <p className="text-gray-600 leading-relaxed">Learn coloring techniques, color theory, and digital art tips to enhance your creative journey.</p>
          </div>

          <div className="group flex flex-col p-8 rounded-3xl bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 hover:border-orange-300 transition-all duration-300 hover:shadow-lg hover:shadow-orange-100">
            <div className="mb-6 flex size-20 items-center justify-center rounded-2xl bg-gradient-to-br from-orange-400 to-orange-500 border border-orange-300 group-hover:scale-110 transition-transform duration-300">
              <Icon name="Heart" className="size-10 text-white" />
            </div>
            <h3 className="mb-3 text-xl font-semibold text-gray-800">Stress Relief</h3>
            <p className="text-gray-600 leading-relaxed">Coloring is a great way to relax, reduce stress, and express your creativity in a mindful way.</p>
          </div>
        </div>

        <div className="text-center">
          <div className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-pink-100 to-cyan-100 rounded-full text-gray-700 font-medium">
            <Icon name="Sparkles" className="h-5 w-5 text-pink-500" />
            <span>We bring the imagery, you bring your imagination!</span>
            <Icon name="Sparkles" className="h-5 w-5 text-cyan-500" />
          </div>
        </div>
      </div>
    </section>
  );
}
