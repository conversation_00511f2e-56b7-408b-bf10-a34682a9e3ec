"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Section as SectionType } from "@/types/blocks/section";
import Icon from "@/components/icon";
import { useState } from "react";

export default function CTA({ section }: { section: SectionType }) {
  const [email, setEmail] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");

  if (section.disabled) {
    return null;
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log("Subscription:", { firstName, lastName, email });
  };

  return (
    <section id={section.name} className="py-20 bg-gradient-to-br from-cyan-50 via-pink-50 to-yellow-50">
      <div className="container">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <Badge className="bg-gradient-to-r from-pink-500 to-cyan-500 text-white border-0 px-4 py-2">
                ✨ FREE COLORING BOOK
              </Badge>

              <h2 className="text-4xl lg:text-5xl font-bold text-gray-800 leading-tight">
                Get the <span className="text-pink-500">Eclectic</span> Coloring Book
                <span className="block">delivered to you</span>
              </h2>

              <div className="space-y-4 text-gray-600">
                <h3 className="text-xl font-semibold text-gray-800">Here's what you get:</h3>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Icon name="Check" className="h-5 w-5 text-green-500 flex-shrink-0" />
                    <span>18-page coloring book with themes like architecture and kawaii</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Icon name="Check" className="h-5 w-5 text-green-500 flex-shrink-0" />
                    <span>Directions for digital coloring in Procreate</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Icon name="Check" className="h-5 w-5 text-green-500 flex-shrink-0" />
                    <span>Access to Subscriber Central for exclusive coloring content</span>
                  </div>
                </div>
              </div>

              <p className="text-gray-600 font-medium">
                Enter your email to receive your Eclectic Coloring Book instantly.
              </p>
            </div>

            <div className="bg-white rounded-3xl p-8 shadow-xl border border-gray-100">
              <div className="text-center mb-6">
                <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-pink-400 to-cyan-400 rounded-2xl mb-4">
                  <Icon name="Download" className="h-10 w-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-800">Get your free coloring book</h3>
                <p className="text-gray-600 mt-2">Sign up and get your <strong>free Yay! Coloring Book</strong>. Plus, gain access to <strong>Subscriber Central</strong> for exclusive coloring content.</p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Input
                    type="text"
                    placeholder="First Name"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-pink-400 focus:outline-none transition-colors"
                  />
                </div>

                <div>
                  <Input
                    type="text"
                    placeholder="Last Name"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-pink-400 focus:outline-none transition-colors"
                  />
                </div>

                <div>
                  <Input
                    type="email"
                    placeholder="Email Address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-pink-400 focus:outline-none transition-colors"
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full py-4 bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white font-semibold rounded-lg text-lg shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  Subscribe
                </Button>
              </form>

              <p className="text-xs text-gray-500 text-center mt-4">
                We respect your privacy. Unsubscribe at any time.
              </p>

              <div className="text-center mt-6">
                <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
                  <span>Built with</span>
                  <strong>Kit</strong>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
