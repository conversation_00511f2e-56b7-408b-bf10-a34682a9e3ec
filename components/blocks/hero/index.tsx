import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import HappyUsers from "./happy-users";
import HeroBg from "./bg";
import { Hero as HeroType } from "@/types/blocks/hero";
import Icon from "@/components/icon";
import Link from "next/link";

export default function Hero({ hero }: { hero: HeroType }) {
  if (hero.disabled) {
    return null;
  }
  const highlightText = hero.highlight_text;
  let texts = null;
  if (highlightText) {
    texts = hero.title?.split(highlightText, 2);
  }
  return (
    <>
      <HeroBg />
      <section className="py-20 bg-white">
        <div className="container">
          {hero.announcement && (
            <div className="flex items-center justify-center mb-8">
              <Badge variant="outline" className="px-4 py-2 text-sm bg-blue-50 border-blue-200 text-blue-700 font-semibold">
                {hero.announcement.label && <span className="mr-2">{hero.announcement.label}</span>}
                {hero.announcement.title}
              </Badge>
            </div>
          )}

          <div className="text-center space-y-8 max-w-4xl mx-auto">
            {hero.title && (
              <h1 className="text-4xl lg:text-6xl font-bold tracking-tight text-foreground">
                {texts && texts.length === 2 ? (
                  <>
                    {texts[0]}
                    <span className="text-primary font-black uppercase">
                      {highlightText}
                    </span>
                    {texts[1]}
                  </>
                ) : (
                  hero.title
                )}
              </h1>
            )}

            {hero.description && (
              <div
                className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed"
                dangerouslySetInnerHTML={{ __html: hero.description }}
              />
            )}

            {hero.tip && (
              <div className="inline-block px-4 py-2 bg-blue-50 text-blue-700 rounded-full text-sm font-medium">
                {hero.tip}
              </div>
            )}

            {hero.buttons && hero.buttons.length > 0 && (
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-6">
                {hero.buttons.map((button, index) => (
                  <Button
                    key={index}
                    size="lg"
                    variant={button.variant || "default"}
                    className="text-lg px-8 py-4"
                    asChild
                  >
                    <Link href={button.url || ""} target={button.target || "_self"}>
                      {button.icon && (
                        <Icon name={button.icon} className="mr-2 h-5 w-5" />
                      )}
                      {button.title}
                    </Link>
                  </Button>
                ))}
              </div>
            )}

            {hero.show_happy_users && <HappyUsers />}
          </div>
        </div>
      </section>
    </>
  );
}
