import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import HappyUsers from "./happy-users";
import HeroBg from "./bg";
import { Hero as HeroType } from "@/types/blocks/hero";
import Icon from "@/components/icon";
import Link from "next/link";

export default function Hero({ hero }: { hero: HeroType }) {
  if (hero.disabled) {
    return null;
  }
  const highlightText = hero.highlight_text;
  let texts = null;
  if (highlightText) {
    texts = hero.title?.split(highlightText, 2);
  }
  return (
    <>
      <HeroBg />
      <section className="py-16 bg-gradient-to-br from-pink-50 via-cyan-50 to-yellow-50">
        <div className="container">
          {hero.show_badge && (
            <div className="flex items-center justify-center mb-8">
              <Badge variant="outline" className="px-4 py-2 text-sm bg-gradient-to-r from-pink-100 to-yellow-100 border-pink-200 text-pink-700 font-semibold">
                ✨ New Collection Available
              </Badge>
            </div>
          )}
          <div className="text-center space-y-8 max-w-6xl mx-auto">
            <div className="flex items-center justify-center mb-6">
              <div className="text-6xl lg:text-8xl font-black tracking-tight">
                <span className="text-pink-500">Y</span>
                <span className="text-cyan-400">A</span>
                <span className="text-yellow-400">Y</span>
                <span className="text-pink-500">!</span>
              </div>
            </div>

            <h1 className="text-4xl lg:text-6xl font-bold tracking-tight text-gray-800">
              <span className="block">Free Printable</span>
              <span className="block bg-gradient-to-r from-pink-600 via-purple-600 to-cyan-600 bg-clip-text text-transparent">
                Coloring Pages
              </span>
              <span className="block">& Birthday Cards</span>
            </h1>

            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Unleash your creativity with our collection of free printable coloring pages and tutorials.
              Perfect for kids and adults who love to color!
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-6">
              <Button size="lg" className="text-lg px-8 py-4 bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white shadow-lg hover:shadow-xl transition-all duration-200">
                Browse Coloring Pages
                <Icon name="ArrowRight" className="ml-2 h-5 w-5" />
              </Button>
              <Button variant="outline" size="lg" className="text-lg px-8 py-4 border-2 border-cyan-400 text-cyan-600 hover:bg-cyan-50 transition-all duration-200">
                <Icon name="Download" className="mr-2 h-5 w-5" />
                Get Free Coloring Book
              </Button>
            </div>

            <div className="pt-8 grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-pink-400 to-pink-500 rounded-full flex items-center justify-center">
                  <Icon name="Palette" className="h-8 w-8 text-white" />
                </div>
                <p className="text-sm font-medium text-gray-700">Animals</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-cyan-400 to-cyan-500 rounded-full flex items-center justify-center">
                  <Icon name="Calendar" className="h-8 w-8 text-white" />
                </div>
                <p className="text-sm font-medium text-gray-700">Holidays</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center">
                  <Icon name="Home" className="h-8 w-8 text-white" />
                </div>
                <p className="text-sm font-medium text-gray-700">Architecture</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-purple-400 to-purple-500 rounded-full flex items-center justify-center">
                  <Icon name="Sparkles" className="h-8 w-8 text-white" />
                </div>
                <p className="text-sm font-medium text-gray-700">Fantasy</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
