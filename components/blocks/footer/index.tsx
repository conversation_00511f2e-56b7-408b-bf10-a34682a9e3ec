import { Footer as FooterType } from "@/types/blocks/footer";
import Icon from "@/components/icon";

export default function Footer({ footer }: { footer: FooterType }) {
  if (footer.disabled) {
    return null;
  }

  return (
    <section id={footer.name} className="bg-gray-50 border-t border-gray-200">
      <div className="container py-16">
        <div className="grid lg:grid-cols-4 gap-8 mb-12">
          <div className="lg:col-span-2">
            <div className="flex items-center gap-3 mb-6">
              <div className="text-4xl font-black tracking-tight">
                <span className="text-pink-500">Y</span>
                <span className="text-cyan-400">A</span>
                <span className="text-yellow-400">Y</span>
                <span className="text-pink-500">!</span>
              </div>
              <div>
                <div className="text-lg font-bold text-gray-800">COLORING</div>
                <div className="text-lg font-bold text-gray-800">PAGES</div>
              </div>
            </div>
            <p className="text-gray-600 mb-6 max-w-md">
              Free printable coloring pages and tutorials for kids and adults.
              We bring the imagery, you bring your imagination!
            </p>
            <div className="flex gap-4">
              <a href="https://www.instagram.com/yaycoloringpages/" className="w-10 h-10 bg-gradient-to-br from-pink-400 to-pink-500 rounded-full flex items-center justify-center text-white hover:scale-110 transition-transform duration-200">
                <Icon name="Instagram" className="h-5 w-5" />
              </a>
              <a href="https://www.facebook.com/yaycoloringpages" className="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center text-white hover:scale-110 transition-transform duration-200">
                <Icon name="Facebook" className="h-5 w-5" />
              </a>
              <a href="https://www.threads.net/@yaycoloringpages" className="w-10 h-10 bg-gradient-to-br from-gray-700 to-gray-800 rounded-full flex items-center justify-center text-white hover:scale-110 transition-transform duration-200">
                <Icon name="MessageCircle" className="h-5 w-5" />
              </a>
              <a href="http://www.pinterest.com/yaycoloringpages" className="w-10 h-10 bg-gradient-to-br from-red-400 to-red-500 rounded-full flex items-center justify-center text-white hover:scale-110 transition-transform duration-200">
                <Icon name="Pin" className="h-5 w-5" />
              </a>
              <a href="https://twitter.com/yaycolorpages" className="w-10 h-10 bg-gradient-to-br from-cyan-400 to-cyan-500 rounded-full flex items-center justify-center text-white hover:scale-110 transition-transform duration-200">
                <Icon name="Twitter" className="h-5 w-5" />
              </a>
            </div>
          </div>

          <div>
            <h3 className="font-bold text-gray-800 mb-4">Coloring Pages</h3>
            <ul className="space-y-3 text-gray-600">
              <li><a href="/coloring-pages" className="hover:text-pink-500 transition-colors">Browse All</a></li>
              <li><a href="/category/animals" className="hover:text-pink-500 transition-colors">Animals</a></li>
              <li><a href="/category/holidays" className="hover:text-pink-500 transition-colors">Holidays</a></li>
              <li><a href="/category/architecture" className="hover:text-pink-500 transition-colors">Architecture</a></li>
              <li><a href="/category/fantasy" className="hover:text-pink-500 transition-colors">Fantasy</a></li>
            </ul>
          </div>

          <div>
            <h3 className="font-bold text-gray-800 mb-4">Resources</h3>
            <ul className="space-y-3 text-gray-600">
              <li><a href="/tools-tricks" className="hover:text-pink-500 transition-colors">Tools & Tricks</a></li>
              <li><a href="/tutorials" className="hover:text-pink-500 transition-colors">Tutorials</a></li>
              <li><a href="/about" className="hover:text-pink-500 transition-colors">About</a></li>
              <li><a href="/contact" className="hover:text-pink-500 transition-colors">Contact</a></li>
              <li><a href="/faq" className="hover:text-pink-500 transition-colors">FAQ</a></li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-200 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-gray-600 text-sm">
              Copyright © 2025 Yay! Coloring Pages
            </div>
            <div className="flex gap-6 text-sm text-gray-600">
              <a href="/privacy-policy" className="hover:text-pink-500 transition-colors">Privacy Policy</a>
              <a href="/affiliate-disclaimer" className="hover:text-pink-500 transition-colors">Affiliate Disclaimer</a>
              <a href="/terms" className="hover:text-pink-500 transition-colors">Terms of Use</a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
